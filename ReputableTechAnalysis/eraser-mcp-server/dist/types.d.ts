import { z } from 'zod';
export declare const EraserConfigSchema: z.ZodObject<{
    apiKey: z.ZodString;
    baseUrl: z.ZodDefault<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    apiKey: string;
    baseUrl: string;
}, {
    apiKey: string;
    baseUrl?: string | undefined;
}>;
export type EraserConfig = z.infer<typeof EraserConfigSchema>;
export declare const RenderPromptRequestSchema: z.ZodObject<{
    text: z.ZodString;
    diagramType: z.ZodOptional<z.ZodEnum<["entity-relationship", "sequence", "flowchart", "cloud-architecture"]>>;
}, "strip", z.ZodType<PERSON>ny, {
    text: string;
    diagramType?: "entity-relationship" | "sequence" | "flowchart" | "cloud-architecture" | undefined;
}, {
    text: string;
    diagramType?: "entity-relationship" | "sequence" | "flowchart" | "cloud-architecture" | undefined;
}>;
export type RenderPromptRequest = z.infer<typeof RenderPromptRequestSchema>;
export declare const RenderElementsRequestSchema: z.ZodObject<{
    text: z.ZodString;
}, "strip", z.ZodTypeAny, {
    text: string;
}, {
    text: string;
}>;
export type RenderElementsRequest = z.infer<typeof RenderElementsRequestSchema>;
export interface EraserResponse {
    success: boolean;
    data?: any;
    error?: string;
    url?: string;
    id?: string;
}
export interface DiagramResult {
    id: string;
    url: string;
    title?: string;
    type?: string;
    createdAt: string;
}
