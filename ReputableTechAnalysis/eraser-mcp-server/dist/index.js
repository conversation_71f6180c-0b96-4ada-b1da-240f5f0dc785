#!/usr/bin/env node
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema, } from '@modelcontextprotocol/sdk/types.js';
import { EraserClient } from './client.js';
import { ERASER_TOOLS, EXAMPLE_DSL, EXAMPLE_PROMPTS } from './tools.js';
import { EraserConfigSchema, RenderPromptRequestSchema, RenderElementsRequestSchema } from './types.js';
class EraserMCPServer {
    server;
    eraserClient = null;
    constructor() {
        this.server = new Server({
            name: 'eraser-mcp-server',
            version: '1.0.0',
        }, {
            capabilities: {
                tools: {},
            },
        });
        this.setupHandlers();
    }
    setupHandlers() {
        this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
            tools: ERASER_TOOLS,
        }));
        this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
            const { name, arguments: args } = request.params;
            // Initialize client if not already done
            if (!this.eraserClient) {
                const apiKey = process.env.ERASER_API_KEY;
                if (!apiKey) {
                    throw new Error('ERASER_API_KEY environment variable is required');
                }
                const config = EraserConfigSchema.parse({
                    apiKey,
                    baseUrl: process.env.ERASER_BASE_URL,
                });
                this.eraserClient = new EraserClient(config);
            }
            switch (name) {
                case 'render_ai_diagram': {
                    const request = RenderPromptRequestSchema.parse(args);
                    const result = await this.eraserClient.renderPrompt(request);
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `✅ AI Diagram Generated Successfully!

**Diagram Details:**
- ID: ${result.id}
- Type: ${result.type}
- Created: ${result.createdAt}
- URL: ${result.url}

**Prompt Used:** "${request.text}"

You can view and edit your diagram at: ${result.url}

**Example prompts you can try:**
${EXAMPLE_PROMPTS.map(p => `• "${p}"`).join('\n')}`,
                            },
                        ],
                    };
                }
                case 'render_dsl_diagram': {
                    const request = RenderElementsRequestSchema.parse(args);
                    const result = await this.eraserClient.renderElements(request);
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `✅ DSL Diagram Generated Successfully!

**Diagram Details:**
- ID: ${result.id}
- Type: ${result.type}
- Created: ${result.createdAt}
- URL: ${result.url}

You can view and edit your diagram at: ${result.url}

**Example DSL syntax:**
\`\`\`
${EXAMPLE_DSL}
\`\`\``,
                            },
                        ],
                    };
                }
                case 'test_eraser_connection': {
                    const isConnected = await this.eraserClient.testConnection();
                    return {
                        content: [
                            {
                                type: 'text',
                                text: isConnected
                                    ? '✅ Connection to Eraser.io API successful! You can now create diagrams.'
                                    : '❌ Connection to Eraser.io API failed. Please check your API key and internet connection.',
                            },
                        ],
                    };
                }
                default:
                    throw new Error(`Unknown tool: ${name}`);
            }
        });
    }
    async run() {
        const transport = new StdioServerTransport();
        await this.server.connect(transport);
        console.error('Eraser MCP Server running on stdio');
    }
}
const server = new EraserMCPServer();
server.run().catch(console.error);
