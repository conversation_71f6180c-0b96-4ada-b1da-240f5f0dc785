import { Tool } from '@modelcontextprotocol/sdk/types.js';
export declare const ERASER_TOOLS: Tool[];
export declare const EXAMPLE_DSL = "\n// Example Eraser DSL syntax:\n// Define entities with relationships\nUser [icon: user] {\n  id: string\n  name: string\n  email: string\n}\n\nOrder [icon: shopping-cart] {\n  id: string\n  userId: string\n  amount: number\n  status: string\n}\n\n// Define relationships\nUser ||--o{ Order : \"places\"\n\n// Comments and styling\nUser.style.fill: \"#e1f5fe\"\nOrder.style.fill: \"#f3e5f5\"\n";
export declare const EXAMPLE_PROMPTS: string[];
