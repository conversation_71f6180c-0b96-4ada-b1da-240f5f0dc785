import { EraserConfig, RenderPromptRequest, RenderElementsRequest, DiagramResult } from './types.js';
export declare class EraserClient {
    private client;
    private config;
    constructor(config: EraserConfig);
    renderPrompt(request: RenderPromptRequest): Promise<DiagramResult>;
    renderElements(request: RenderElementsRequest): Promise<DiagramResult>;
    testConnection(): Promise<boolean>;
}
