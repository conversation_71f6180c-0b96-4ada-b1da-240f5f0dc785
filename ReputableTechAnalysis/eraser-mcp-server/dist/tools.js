export const ERASER_TOOLS = [
    {
        name: 'render_ai_diagram',
        description: 'Generate an AI diagram from a text prompt using Eraser.io',
        inputSchema: {
            type: 'object',
            properties: {
                text: {
                    type: 'string',
                    description: 'The prompt text describing what diagram to create (e.g., "Draw a data model for a Twitter clone")',
                },
                diagramType: {
                    type: 'string',
                    enum: ['entity-relationship', 'sequence', 'flowchart', 'cloud-architecture'],
                    description: 'Optional: Specify the type of diagram to generate',
                },
            },
            required: ['text'],
        },
    },
    {
        name: 'render_dsl_diagram',
        description: 'Create a diagram from Eraser DSL (Domain Specific Language) syntax',
        inputSchema: {
            type: 'object',
            properties: {
                text: {
                    type: 'string',
                    description: 'The Eraser DSL syntax defining the diagram elements and relationships',
                },
            },
            required: ['text'],
        },
    },
    {
        name: 'test_eraser_connection',
        description: 'Test the connection to Eraser.io API to verify authentication and service availability',
        inputSchema: {
            type: 'object',
            properties: {},
        },
    },
];
export const EXAMPLE_DSL = `
// Example Eraser DSL syntax:
// Define entities with relationships
User [icon: user] {
  id: string
  name: string
  email: string
}

Order [icon: shopping-cart] {
  id: string
  userId: string
  amount: number
  status: string
}

// Define relationships
User ||--o{ Order : "places"

// Comments and styling
User.style.fill: "#e1f5fe"
Order.style.fill: "#f3e5f5"
`;
export const EXAMPLE_PROMPTS = [
    'Draw a microservices architecture for an e-commerce platform',
    'Create an entity relationship diagram for a social media app',
    'Design a sequence diagram showing user authentication flow',
    'Generate a flowchart for the software deployment process',
    'Draw a cloud architecture diagram using AWS services',
];
