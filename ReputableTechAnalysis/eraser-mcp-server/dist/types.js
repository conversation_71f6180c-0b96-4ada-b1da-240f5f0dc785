import { z } from 'zod';
export const EraserConfigSchema = z.object({
    apiKey: z.string().min(1, 'API key is required'),
    baseUrl: z.string().url().default('https://app.eraser.io/api'),
});
export const RenderPromptRequestSchema = z.object({
    text: z.string().min(1, 'Prompt text is required'),
    diagramType: z.enum(['entity-relationship', 'sequence', 'flowchart', 'cloud-architecture']).optional(),
});
export const RenderElementsRequestSchema = z.object({
    text: z.string().min(1, 'Eraser DSL text is required'),
});
