import axios from 'axios';
export class EraserClient {
    client;
    config;
    constructor(config) {
        this.config = config;
        this.client = axios.create({
            baseURL: config.baseUrl,
            headers: {
                'Authorization': `Bearer ${config.apiKey}`,
                'Content-Type': 'application/json',
            },
            timeout: 30000, // 30 second timeout
        });
        // Add response interceptor for error handling
        this.client.interceptors.response.use((response) => response, (error) => {
            if (error.response?.status === 401) {
                throw new Error('Invalid API key or unauthorized access');
            }
            if (error.response?.status === 403) {
                throw new Error('Access forbidden - check if you have a paid plan');
            }
            if (error.response?.status === 429) {
                throw new Error('Rate limit exceeded - please try again later');
            }
            throw error;
        });
    }
    async renderPrompt(request) {
        try {
            const response = await this.client.post('/render/prompt', {
                text: request.text,
                ...(request.diagramType && { diagramType: request.diagramType }),
            });
            if (!response.data.success) {
                throw new Error(response.data.error || 'Failed to render diagram from prompt');
            }
            return {
                id: response.data.id || 'unknown',
                url: response.data.url || '',
                title: request.text.substring(0, 50) + '...',
                type: request.diagramType || 'ai-generated',
                createdAt: new Date().toISOString(),
            };
        }
        catch (error) {
            if (axios.isAxiosError(error)) {
                throw new Error(`API request failed: ${error.message}`);
            }
            throw error;
        }
    }
    async renderElements(request) {
        try {
            const response = await this.client.post('/render/elements', {
                text: request.text,
            });
            if (!response.data.success) {
                throw new Error(response.data.error || 'Failed to render diagram from elements');
            }
            return {
                id: response.data.id || 'unknown',
                url: response.data.url || '',
                title: 'DSL Diagram',
                type: 'dsl-generated',
                createdAt: new Date().toISOString(),
            };
        }
        catch (error) {
            if (axios.isAxiosError(error)) {
                throw new Error(`API request failed: ${error.message}`);
            }
            throw error;
        }
    }
    async testConnection() {
        try {
            // Test with a simple prompt
            await this.renderPrompt({ text: 'Simple test diagram' });
            return true;
        }
        catch (error) {
            console.error('Connection test failed:', error);
            return false;
        }
    }
}
