# Eraser.io MCP Server

An MCP (Model Context Protocol) server that integrates with Eraser.io to generate diagrams using AI prompts and DSL syntax.

## Features

- **AI Diagram Generation**: Create diagrams from natural language prompts
- **DSL Support**: Generate diagrams using Eraser's Domain Specific Language
- **Connection Testing**: Verify API connectivity and authentication
- **Type Safety**: Full TypeScript implementation with Zod validation

## Prerequisites

- Node.js >= 18
- Eraser.io account with paid plan
- Eraser.io API key

## Installation

1. Clone or download this MCP server
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy the environment file and configure your API key:
   ```bash
   cp .env.example .env
   ```

4. Edit `.env` file with your Eraser.io API key:
   ```
   ERASER_API_KEY=your_api_key_here
   ```

5. Build the server:
   ```bash
   npm run build
   ```

## Usage

### Running the Server

For development:
```bash
npm run dev
```

For production:
```bash
npm start
```

### Available Tools

#### `render_ai_diagram`
Generate an AI diagram from a text prompt.

**Parameters:**
- `text` (required): The prompt describing what diagram to create
- `diagramType` (optional): Specify diagram type ('entity-relationship', 'sequence', 'flowchart', 'cloud-architecture')

**Example:**
```json
{
  "text": "Draw a microservices architecture for an e-commerce platform",
  "diagramType": "cloud-architecture"
}
```

#### `render_dsl_diagram`
Create a diagram from Eraser DSL syntax.

**Parameters:**
- `text` (required): The Eraser DSL syntax

**Example:**
```json
{
  "text": "User [icon: user] { id: string, name: string } Order [icon: cart] { id: string, userId: string } User ||--o{ Order : 'places'"
}
```

#### `test_eraser_connection`
Test the connection to Eraser.io API.

**Parameters:** None

## MCP Client Configuration

Add this server to your MCP client configuration:

```json
{
  "mcpServers": {
    "eraser": {
      "command": "node",
      "args": ["/path/to/eraser-mcp-server/dist/index.js"],
      "env": {
        "ERASER_API_KEY": "your_api_key_here"
      }
    }
  }
}
```

## Example Prompts

- "Draw a data model for a Twitter clone"
- "Create a sequence diagram for user authentication"
- "Generate a flowchart for the CI/CD pipeline"
- "Design a cloud architecture using AWS services"
- "Show the microservices architecture for an online store"

## Eraser DSL Examples

```dsl
// Simple entity relationship
User [icon: user] {
  id: string
  name: string
  email: string
}

Order [icon: shopping-cart] {
  id: string
  userId: string
  amount: number
}

User ||--o{ Order : "places"
```

## Troubleshooting

### Common Issues

1. **Authentication Error**: Verify your API key is correct and your account has a paid plan
2. **Connection Timeout**: Check your internet connection and Eraser.io service status
3. **Invalid DSL**: Ensure your DSL syntax follows Eraser.io documentation

### Error Messages

- `Invalid API key or unauthorized access`: Check your ERASER_API_KEY
- `Access forbidden - check if you have a paid plan`: Upgrade your Eraser.io account
- `Rate limit exceeded`: Wait before making more requests

## Development

### Scripts

- `npm run dev`: Start development server with hot reload
- `npm run build`: Build TypeScript to JavaScript
- `npm start`: Run built server

### Project Structure

```
src/
├── index.ts      # Main MCP server implementation
├── client.ts     # Eraser.io API client
├── tools.ts      # MCP tool definitions
└── types.ts      # TypeScript types and schemas
```

## License

MIT